/// <reference types="vite/client" />

// Vue 模块声明
declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const component: DefineComponent<{}, {}, any>;
  export default component;
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_ENV: "development" | "test" | "staging" | "production";
  readonly VITE_BASE_URL: string;
  readonly VITE_API_TIMEOUT: string;
  readonly VITE_ENABLE_MOCK: string;
  readonly VITE_ENABLE_DEVTOOLS: string;
  readonly VITE_ENABLE_CONSOLE: string;
  readonly VITE_PROXY_TARGET: string;
  readonly VITE_JUMP_URL: string;
  readonly VITE_ASSETS_URL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 全局变量声明
declare const __APP_ENV__: string;
declare const __APP_TITLE__: string;
